import { Container, Title, Text } from '@mantine/core'

import { DashboardLayout } from '@/components/layouts/DashboardLayout'

export default function ProductsPage() {
  return (
    <DashboardLayout>
      <Container size="xl">
        <Title order={1} mb="md">
          Products Catalog
        </Title>
        <Text c="dimmed" mb="xl">
          Manage your product inventory, pricing, and catalog.
        </Text>
        
        <Text>
          This is the Products page. Here you would typically see a grid or table of products,
          inventory management tools, and product editing capabilities.
        </Text>
      </Container>
    </DashboardLayout>
  )
}
