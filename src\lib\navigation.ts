import {
  ChartSquare,
  User,
  Box,
  CartLarge2,
  Settings,
} from '@solar-icons/react'

import type { NavigationSection } from '@/types/navigation'

export const navigationConfig: NavigationSection[] = [
  {
    items: [
      {
        label: 'Overview',
        href: '/dashboard',
        icon: ChartSquare,
        description: 'Dashboard analytics and insights',
      },
      {
        label: 'Users',
        href: '/dashboard/users',
        icon: User,
        description: 'Manage user accounts and permissions',
      },
      {
        label: 'Products',
        href: '/dashboard/products',
        icon: Box,
        description: 'Product catalog and inventory',
      },
      {
        label: 'Orders',
        href: '/dashboard/orders',
        icon: CartLarge2,
        description: 'Order management and tracking',
        badge: 12,
      },
      {
        label: 'Settings',
        href: '/dashboard/settings',
        icon: Settings,
        description: 'Application settings and configuration',
      },
    ],
  },
]

export const mockUser = {
  name: '<PERSON>',
  email: '<EMAIL>',
  role: 'Administrator',
  avatar: undefined,
}
