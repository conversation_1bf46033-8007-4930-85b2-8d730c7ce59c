'use client'

import { Text } from '@mantine/core'
import {
  ChartSquare,
  User,
  Box,
  CartLarge2,
  CourseUp,
  CourseDown,
} from '@solar-icons/react'

import { DashboardLayout } from '@/components/layouts/DashboardLayout'

import classes from './Dashboard.module.css'

const statsData = [
  {
    title: 'Total Users',
    value: '1,234',
    change: '+12.5%',
    positive: true,
    icon: User,
  },
  {
    title: 'Products',
    value: '456',
    change: '+3.2%',
    positive: true,
    icon: Box,
  },
  {
    title: 'Orders',
    value: '789',
    change: '-2.1%',
    positive: false,
    icon: CartLarge2,
  },
  {
    title: 'Revenue',
    value: '$12,345',
    change: '+8.7%',
    positive: true,
    icon: ChartSquare,
  },
]

export function Dashboard() {
  return (
    <DashboardLayout>
      <div className={classes.dashboard}>
        {/* Welcome Section */}
        <div className={classes.welcomeSection}>
          <h1 className={classes.welcomeTitle}>Welcome back, <PERSON>!</h1>
          <p className={classes.welcomeText}>
            Here's what's happening with your business today.
          </p>
        </div>

        {/* Page Header */}
        <div className={classes.header}>
          <h2 className={classes.title}>Dashboard Overview</h2>
          <Text className={classes.subtitle}>
            Monitor your key metrics and business performance
          </Text>
        </div>

        {/* Stats Grid */}
        <div className={classes.grid}>
          {statsData.map((stat) => {
            const IconComponent = stat.icon
            const ChangeIcon = stat.positive ? CourseUp : CourseDown

            return (
              <div key={stat.title} className={classes.card}>
                <div className={classes.cardHeader}>
                  <Text className={classes.cardTitle}>{stat.title}</Text>
                  <div className={classes.cardIcon}>
                    <IconComponent size={20} />
                  </div>
                </div>
                
                <div className={classes.cardValue}>{stat.value}</div>
                
                <div
                  className={classes.cardChange}
                  data-positive={stat.positive}
                >
                  <ChangeIcon size={14} style={{ marginRight: 4 }} />
                  {stat.change} from last month
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </DashboardLayout>
  )
}
