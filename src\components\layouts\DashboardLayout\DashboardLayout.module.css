.layout {
  min-height: 100vh;
  background: var(--mantine-color-gray-0);
}

[data-mantine-color-scheme="dark"] .layout {
  background: var(--mantine-color-dark-8);
}

.main {
  padding: var(--mantine-spacing-lg);
  min-height: calc(100vh - 60px);
  overflow-x: auto;
}

.mobileOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 150;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobileOverlay[data-opened="true"] {
  opacity: 1;
  visibility: visible;
}

/* Mobile styles */
@media (max-width: 768px) {
  .main {
    padding: var(--mantine-spacing-md);
  }
}

/* Responsive sidebar widths */
.layout[data-sidebar-collapsed="false"] .main {
  margin-left: 280px;
}

.layout[data-sidebar-collapsed="true"] .main {
  margin-left: 60px;
}

@media (max-width: 768px) {
  .layout[data-sidebar-collapsed="false"] .main,
  .layout[data-sidebar-collapsed="true"] .main {
    margin-left: 0;
  }
}
