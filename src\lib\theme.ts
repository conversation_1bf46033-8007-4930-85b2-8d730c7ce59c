'use client'

import { createTheme, MantineColorsTuple, MantineThemeOverride } from '@mantine/core'

const greenColors: MantineColorsTuple = [
  'oklch(0.982 0.018 155.826)',
  'oklch(0.962 0.044 156.743)',
  'oklch(0.925 0.084 155.995)',
  'oklch(0.871 0.15 154.449)',
  'oklch(0.792 0.209 151.711)',
  'oklch(0.723 0.219 149.579)',
  'oklch(0.627 0.194 149.214)',
  'oklch(0.527 0.154 150.069)',
  'oklch(0.448 0.119 151.328)',
  'oklch(0.393 0.095 152.535)',
  'oklch(0.266 0.065 152.934)',
]

const yellowColors: MantineColorsTuple = [
  'oklch(0.987 0.026 102.212)',
  'oklch(0.973 0.071 103.193)',
  'oklch(0.945 0.129 101.54)',
  'oklch(0.905 0.182 98.111)',
  'oklch(0.852 0.199 91.936)',
  'oklch(0.795 0.184 86.047)',
  'oklch(0.681 0.162 75.834)',
  'oklch(0.554 0.135 66.442)',
  'oklch(0.476 0.114 61.907)',
  'oklch(0.421 0.095 57.708)',
  'oklch(0.286 0.066 53.813)',
]

const theme: MantineThemeOverride = createTheme({
  // Colors
  colors: {
    primary: greenColors,
    secondary: yellowColors,
  },
  primaryColor: 'primary',
  black: 'var(--mantine-color-dark-5)',
  defaultGradient: {
    from: 'primary',
    to: 'secondary',
    deg: 90,
  },
  defaultRadius: 'md',

  // Typography
  fontFamily: 'var(--font-sans), sans-serif',
})

export default theme
