.header {
  border-bottom: 1px solid var(--mantine-color-gray-3);
  background: var(--mantine-color-white);
  padding: var(--mantine-spacing-md) var(--mantine-spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  position: sticky;
  top: 0;
  z-index: 100;
}

[data-mantine-color-scheme="dark"] .header {
  background: var(--mantine-color-dark-7);
  border-bottom-color: var(--mantine-color-dark-4);
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: var(--mantine-spacing-md);
}

.headerRight {
  display: flex;
  align-items: center;
  gap: var(--mantine-spacing-sm);
}

.menuButton {
  display: none;
}

.logo {
  font-weight: 700;
  font-size: var(--mantine-font-size-lg);
  color: var(--mantine-color-primary-6);
  text-decoration: none;
}

.userSection {
  display: flex;
  align-items: center;
  gap: var(--mantine-spacing-xs);
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-sm);
  border-radius: var(--mantine-radius-md);
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.userSection:hover {
  background: var(--mantine-color-gray-1);
}

[data-mantine-color-scheme="dark"] .userSection:hover {
  background: var(--mantine-color-dark-6);
}

.userInfo {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
}

.userName {
  font-weight: 600;
  font-size: var(--mantine-font-size-sm);
  line-height: 1.2;
}

.userRole {
  font-size: var(--mantine-font-size-xs);
  color: var(--mantine-color-dimmed);
  line-height: 1.2;
}

.notificationButton {
  position: relative;
}

.notificationBadge {
  position: absolute;
  top: -2px;
  right: -2px;
  min-width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--mantine-color-red-6);
  color: white;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--mantine-color-white);
}

[data-mantine-color-scheme="dark"] .notificationBadge {
  border-color: var(--mantine-color-dark-7);
}

/* Mobile styles */
@media (max-width: 768px) {
  .menuButton {
    display: block;
  }
  
  .userInfo {
    display: none;
  }
  
  .header {
    padding: var(--mantine-spacing-sm) var(--mantine-spacing-md);
  }
}
