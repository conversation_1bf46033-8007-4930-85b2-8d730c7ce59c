import { Container, Title, Text } from '@mantine/core'

import { DashboardLayout } from '@/components/layouts/DashboardLayout'

export default function UsersPage() {
  return (
    <DashboardLayout>
      <Container size="xl">
        <Title order={1} mb="md">
          Users Management
        </Title>
        <Text c="dimmed" mb="xl">
          Manage user accounts, permissions, and access controls.
        </Text>
        
        <Text>
          This is the Users page. Here you would typically see a table of users,
          search functionality, and user management tools.
        </Text>
      </Container>
    </DashboardLayout>
  )
}
