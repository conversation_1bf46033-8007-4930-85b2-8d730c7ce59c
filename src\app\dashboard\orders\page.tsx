import { Container, Title, Text, Badge } from '@mantine/core'

import { DashboardLayout } from '@/components/layouts/DashboardLayout'

export default function OrdersPage() {
  return (
    <DashboardLayout>
      <Container size="xl">
        <Title order={1} mb="md">
          Orders Management
        </Title>
        <Text c="dimmed" mb="xl">
          Track and manage customer orders, shipping, and fulfillment.
        </Text>
        
        <Badge color="red" size="lg" mb="md">
          12 Pending Orders
        </Badge>
        
        <Text>
          This is the Orders page. Here you would typically see a table of orders,
          order status tracking, and fulfillment management tools.
        </Text>
      </Container>
    </DashboardLayout>
  )
}
