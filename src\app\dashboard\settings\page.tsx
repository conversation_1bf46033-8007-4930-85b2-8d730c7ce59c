import { Container, Title, Text } from '@mantine/core'

import { DashboardLayout } from '@/components/layouts/DashboardLayout'

export default function SettingsPage() {
  return (
    <DashboardLayout>
      <Container size="xl">
        <Title order={1} mb="md">
          Settings
        </Title>
        <Text c="dimmed" mb="xl">
          Configure application settings, preferences, and system options.
        </Text>
        
        <Text>
          This is the Settings page. Here you would typically see various configuration
          options, user preferences, and system settings.
        </Text>
      </Container>
    </DashboardLayout>
  )
}
