'use client'

import { useState } from 'react'
import { AppShell } from '@mantine/core'
import { useDisclosure, useMediaQuery } from '@mantine/hooks'

import { Header } from '@/components/navigation/Header'
import { Sidebar } from '@/components/navigation/Sidebar'
import { navigationConfig, mockUser } from '@/lib/navigation'

import classes from './DashboardLayout.module.css'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileOpened, { toggle: toggleMobile, close: closeMobile }] = useDisclosure(false)
  const isMobile = useMediaQuery('(max-width: 768px)')

  const handleSidebarToggle = () => {
    if (isMobile) {
      toggleMobile()
    } else {
      setSidebarCollapsed(!sidebarCollapsed)
    }
  }

  const handleOverlayClick = () => {
    if (isMobile) {
      closeMobile()
    }
  }

  return (
    <div
      className={classes.layout}
      data-sidebar-collapsed={sidebarCollapsed}
    >
      <AppShell
        navbar={{
          width: sidebarCollapsed ? 60 : 280,
          breakpoint: 'md',
          collapsed: { mobile: !mobileOpened, desktop: false },
        }}
        header={{ height: 60 }}
        padding={0}
      >
        <AppShell.Header>
          <Header
            user={mockUser}
            onMenuClick={handleSidebarToggle}
            notificationCount={3}
          />
        </AppShell.Header>

        <AppShell.Navbar>
          <Sidebar
            navigation={navigationConfig}
            collapsed={sidebarCollapsed}
            mobileOpened={mobileOpened}
            onCollapseToggle={handleSidebarToggle}
          />
        </AppShell.Navbar>

        <AppShell.Main className={classes.main}>
          {children}
        </AppShell.Main>
      </AppShell>

      {/* Mobile overlay */}
      {isMobile && (
        <div
          className={classes.mobileOverlay}
          data-opened={mobileOpened}
          onClick={handleOverlayClick}
        />
      )}
    </div>
  )
}
