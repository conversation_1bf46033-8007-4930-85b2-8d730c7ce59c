.sidebar {
  background: var(--mantine-color-white);
  border-right: 1px solid var(--mantine-color-gray-3);
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
}

[data-mantine-color-scheme="dark"] .sidebar {
  background: var(--mantine-color-dark-7);
  border-right-color: var(--mantine-color-dark-4);
}

.sidebarContent {
  padding: var(--mantine-spacing-md);
  flex: 1;
  overflow-y: auto;
}

.sidebarHeader {
  padding: var(--mantine-spacing-md);
  border-bottom: 1px solid var(--mantine-color-gray-3);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 60px;
}

[data-mantine-color-scheme="dark"] .sidebarHeader {
  border-bottom-color: var(--mantine-color-dark-4);
}

.collapseButton {
  transition: transform 0.2s ease;
}

.collapseButton[data-collapsed="true"] {
  transform: rotate(180deg);
}

.navSection {
  margin-bottom: var(--mantine-spacing-lg);
}

.sectionTitle {
  font-size: var(--mantine-font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--mantine-color-dimmed);
  margin-bottom: var(--mantine-spacing-sm);
  padding: 0 var(--mantine-spacing-xs);
}

.navLink {
  display: flex;
  align-items: center;
  gap: var(--mantine-spacing-sm);
  padding: var(--mantine-spacing-sm) var(--mantine-spacing-xs);
  border-radius: var(--mantine-radius-md);
  text-decoration: none;
  color: var(--mantine-color-gray-7);
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
  margin-bottom: var(--mantine-spacing-xs);
}

[data-mantine-color-scheme="dark"] .navLink {
  color: var(--mantine-color-gray-3);
}

.navLink:hover {
  background: var(--mantine-color-gray-1);
  color: var(--mantine-color-gray-9);
}

[data-mantine-color-scheme="dark"] .navLink:hover {
  background: var(--mantine-color-dark-6);
  color: var(--mantine-color-gray-1);
}

.navLink[data-active="true"] {
  background: var(--mantine-color-primary-1);
  color: var(--mantine-color-primary-7);
  font-weight: 600;
}

[data-mantine-color-scheme="dark"] .navLink[data-active="true"] {
  background: var(--mantine-color-primary-9);
  color: var(--mantine-color-primary-2);
}

.navLink[data-active="true"]::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--mantine-color-primary-6);
  border-radius: 0 2px 2px 0;
}

.navIcon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navContent {
  flex: 1;
  min-width: 0;
}

.navLabel {
  font-size: var(--mantine-font-size-sm);
  line-height: 1.2;
  margin-bottom: 2px;
}

.navDescription {
  font-size: var(--mantine-font-size-xs);
  color: var(--mantine-color-dimmed);
  line-height: 1.2;
  opacity: 0.8;
}

.navBadge {
  background: var(--mantine-color-red-6);
  color: white;
  font-size: var(--mantine-font-size-xs);
  font-weight: 600;
  padding: 2px 6px;
  border-radius: var(--mantine-radius-sm);
  min-width: 18px;
  text-align: center;
  flex-shrink: 0;
}

/* Collapsed state */
.sidebar[data-collapsed="true"] {
  width: 60px;
}

.sidebar[data-collapsed="true"] .navContent,
.sidebar[data-collapsed="true"] .navBadge,
.sidebar[data-collapsed="true"] .sectionTitle {
  display: none;
}

.sidebar[data-collapsed="true"] .navLink {
  justify-content: center;
  padding: var(--mantine-spacing-sm);
}

.sidebar[data-collapsed="true"] .sidebarContent {
  padding: var(--mantine-spacing-sm);
}

/* Mobile styles */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 200;
    width: 280px;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar[data-mobile-opened="true"] {
    transform: translateX(0);
  }
}
