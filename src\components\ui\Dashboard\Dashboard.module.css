.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  margin-bottom: var(--mantine-spacing-xl);
}

.title {
  font-size: var(--mantine-font-size-xl);
  font-weight: 700;
  color: var(--mantine-color-gray-9);
  margin-bottom: var(--mantine-spacing-xs);
}

[data-mantine-color-scheme="dark"] .title {
  color: var(--mantine-color-gray-1);
}

.subtitle {
  color: var(--mantine-color-dimmed);
  font-size: var(--mantine-font-size-md);
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--mantine-spacing-lg);
  margin-bottom: var(--mantine-spacing-xl);
}

.card {
  background: var(--mantine-color-white);
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-md);
  padding: var(--mantine-spacing-lg);
  transition: all 0.2s ease;
}

[data-mantine-color-scheme="dark"] .card {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-4);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--mantine-shadow-md);
}

.cardHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--mantine-spacing-md);
}

.cardTitle {
  font-weight: 600;
  font-size: var(--mantine-font-size-md);
  color: var(--mantine-color-gray-8);
}

[data-mantine-color-scheme="dark"] .cardTitle {
  color: var(--mantine-color-gray-2);
}

.cardIcon {
  padding: var(--mantine-spacing-xs);
  border-radius: var(--mantine-radius-md);
  background: var(--mantine-color-primary-1);
  color: var(--mantine-color-primary-6);
}

[data-mantine-color-scheme="dark"] .cardIcon {
  background: var(--mantine-color-primary-9);
  color: var(--mantine-color-primary-3);
}

.cardValue {
  font-size: 2rem;
  font-weight: 700;
  color: var(--mantine-color-gray-9);
  margin-bottom: var(--mantine-spacing-xs);
}

[data-mantine-color-scheme="dark"] .cardValue {
  color: var(--mantine-color-gray-1);
}

.cardChange {
  font-size: var(--mantine-font-size-sm);
  font-weight: 500;
}

.cardChange[data-positive="true"] {
  color: var(--mantine-color-green-6);
}

.cardChange[data-positive="false"] {
  color: var(--mantine-color-red-6);
}

.welcomeSection {
  background: linear-gradient(135deg, var(--mantine-color-primary-6), var(--mantine-color-secondary-6));
  border-radius: var(--mantine-radius-lg);
  padding: var(--mantine-spacing-xl);
  color: white;
  margin-bottom: var(--mantine-spacing-xl);
}

.welcomeTitle {
  font-size: var(--mantine-font-size-xl);
  font-weight: 700;
  margin-bottom: var(--mantine-spacing-xs);
}

.welcomeText {
  opacity: 0.9;
  font-size: var(--mantine-font-size-md);
}

/* Mobile styles */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
    gap: var(--mantine-spacing-md);
  }
  
  .welcomeSection {
    padding: var(--mantine-spacing-lg);
  }
  
  .cardValue {
    font-size: 1.5rem;
  }
}
