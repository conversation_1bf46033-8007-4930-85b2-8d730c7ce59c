'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ActionIcon, Text } from '@mantine/core'
import { ArrowLeft } from '@solar-icons/react'

import type { NavigationSection } from '@/types/navigation'

import classes from './Sidebar.module.css'

interface SidebarProps {
  navigation: NavigationSection[]
  collapsed?: boolean
  mobileOpened?: boolean
  onCollapseToggle?: () => void
}

export function Sidebar({
  navigation,
  collapsed = false,
  mobileOpened = false,
  onCollapseToggle,
}: SidebarProps) {
  const pathname = usePathname()

  return (
    <nav
      className={classes.sidebar}
      data-collapsed={collapsed}
      data-mobile-opened={mobileOpened}
    >
      <div className={classes.sidebarHeader}>
        {!collapsed && (
          <Text fw={700} size="lg" c="primary">
            Dashboard
          </Text>
        )}
        <ActionIcon
          variant="subtle"
          size="sm"
          onClick={onCollapseToggle}
          className={classes.collapseButton}
          data-collapsed={collapsed}
          aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          <ArrowLeft size={16} />
        </ActionIcon>
      </div>

      <div className={classes.sidebarContent}>
        {navigation.map((section, sectionIndex) => (
          <div key={sectionIndex} className={classes.navSection}>
            {section.title && !collapsed && (
              <Text className={classes.sectionTitle}>{section.title}</Text>
            )}
            
            {section.items.map((item) => {
              const isActive = pathname === item.href
              const IconComponent = item.icon

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={classes.navLink}
                  data-active={isActive}
                >
                  <div className={classes.navIcon}>
                    <IconComponent
                      size={20}
                      weight={isActive ? 'Bold' : 'Linear'}
                    />
                  </div>
                  
                  <div className={classes.navContent}>
                    <div className={classes.navLabel}>{item.label}</div>
                    {item.description && (
                      <div className={classes.navDescription}>
                        {item.description}
                      </div>
                    )}
                  </div>
                  
                  {item.badge && (
                    <div className={classes.navBadge}>
                      {typeof item.badge === 'number' && item.badge > 99
                        ? '99+'
                        : item.badge}
                    </div>
                  )}
                </Link>
              )
            })}
          </div>
        ))}
      </div>
    </nav>
  )
}
