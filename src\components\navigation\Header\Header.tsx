'use client'

import Link from 'next/link'
import { ActionIcon, Avatar, Group, Text, useMantineColorScheme } from '@mantine/core'
import { Bell, HamburgerMenu, Moon, Sun } from '@solar-icons/react'

import type { UserProfile } from '@/types/navigation'

import classes from './Header.module.css'

interface HeaderProps {
  user: UserProfile
  onMenuClick?: () => void
  notificationCount?: number
}

export function Header({ user, onMenuClick, notificationCount = 0 }: HeaderProps) {
  const { colorScheme, toggleColorScheme } = useMantineColorScheme()

  return (
    <header className={classes.header}>
      <div className={classes.headerLeft}>
        <ActionIcon
          variant="subtle"
          size="lg"
          className={classes.menuButton}
          onClick={onMenuClick}
          aria-label="Toggle navigation menu"
        >
          <HamburgerMenu size={20} />
        </ActionIcon>
        
        <Link href="/dashboard" className={classes.logo}>
          Dashboard
        </Link>
      </div>

      <div className={classes.headerRight}>
        <ActionIcon
          variant="subtle"
          size="lg"
          onClick={toggleColorScheme}
          aria-label={`Switch to ${colorScheme === 'dark' ? 'light' : 'dark'} mode`}
        >
          {colorScheme === 'dark' ? <Sun size={20} /> : <Moon size={20} />}
        </ActionIcon>

        <div className={classes.notificationButton}>
          <ActionIcon variant="subtle" size="lg" aria-label="Notifications">
            <Bell size={20} />
          </ActionIcon>
          {notificationCount > 0 && (
            <div className={classes.notificationBadge}>
              {notificationCount > 99 ? '99+' : notificationCount}
            </div>
          )}
        </div>

        <Group className={classes.userSection} gap="xs">
          <div className={classes.userInfo}>
            <Text className={classes.userName}>{user.name}</Text>
            {user.role && <Text className={classes.userRole}>{user.role}</Text>}
          </div>
          <Avatar
            src={user.avatar}
            alt={user.name}
            size="sm"
            radius="xl"
            color="primary"
          >
            {user.name.charAt(0).toUpperCase()}
          </Avatar>
        </Group>
      </div>
    </header>
  )
}
