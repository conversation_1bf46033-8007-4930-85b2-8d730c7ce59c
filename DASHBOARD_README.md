# Modern Dashboard Layout

A modern dashboard layout inspired by Minimal UI's design approach, built with Mantine components and following the user's established preferences.

## Features

### 🎨 **Design & Layout**
- **AppShell Layout**: Uses Mantine's AppShell component as the main container
- **Collapsible Sidebar**: Responsive navigation with expand/collapse functionality
- **Clean Header**: User profile section, notifications, and theme toggle
- **Responsive Design**: Mobile-first approach with proper breakpoints
- **Minimal UI Inspired**: Clean, professional aesthetic similar to Minimal UI

### 🧭 **Navigation**
- **Sidebar Navigation**: NavLink components with solar-icons
- **Active State Styling**: Visual indication of current page
- **Badge Support**: Notification badges for menu items (e.g., pending orders)
- **Mobile Responsive**: Overlay navigation for mobile devices

### 🎯 **Component Structure**
```
src/
├── components/
│   ├── layouts/
│   │   └── DashboardLayout/          # Main layout component
│   ├── navigation/
│   │   ├── Header/                   # Header with user profile & notifications
│   │   └── Sidebar/                  # Collapsible sidebar navigation
│   └── ui/
│       └── Dashboard/                # Dashboard content component
├── lib/
│   ├── navigation.ts                 # Navigation configuration
│   └── theme.ts                      # Updated theme with dashboard styles
└── types/
    └── navigation.ts                 # TypeScript interfaces
```

### 📱 **Responsive Behavior**
- **Desktop**: Collapsible sidebar (280px → 60px)
- **Mobile**: Overlay sidebar with backdrop
- **Breakpoint**: 768px (md breakpoint)

### 🎨 **Styling Approach**
- **CSS Modules**: Component-specific styling with `.module.css` files
- **Mantine Theme**: Global theme configuration for colors, spacing, typography
- **Design System**: Consistent with Mantine's design principles

## Navigation Structure

The dashboard includes the following navigation items:

- **Overview** (`/dashboard`) - Dashboard analytics and insights
- **Users** (`/dashboard/users`) - Manage user accounts and permissions  
- **Products** (`/dashboard/products`) - Product catalog and inventory
- **Orders** (`/dashboard/orders`) - Order management and tracking (with badge)
- **Settings** (`/dashboard/settings`) - Application settings and configuration

## Key Components

### DashboardLayout
Main layout wrapper that provides:
- AppShell structure
- Sidebar state management
- Mobile responsive behavior
- Header and navigation integration

### Header
Top navigation bar featuring:
- Logo/brand
- Mobile menu toggle
- Theme switcher (dark/light mode)
- Notification bell with badge
- User profile section with avatar

### Sidebar
Collapsible navigation panel with:
- Navigation links with icons
- Active state indication
- Collapse/expand functionality
- Mobile overlay behavior
- Badge support for notifications

## Usage

```tsx
import { DashboardLayout } from '@/components/layouts/DashboardLayout'

export default function MyPage() {
  return (
    <DashboardLayout>
      <YourPageContent />
    </DashboardLayout>
  )
}
```

## Customization

### Adding Navigation Items
Edit `src/lib/navigation.ts` to add new navigation items:

```typescript
{
  label: 'New Page',
  href: '/dashboard/new-page',
  icon: YourIcon,
  description: 'Page description',
  badge: 5, // Optional badge
}
```

### Styling
- Component styles: Edit corresponding `.module.css` files
- Global theme: Update `src/lib/theme.ts`
- Colors: Modify color tuples in theme configuration

### User Profile
Update the mock user data in `src/lib/navigation.ts` or integrate with your authentication system.

## Technical Details

- **TypeScript**: Full type safety with proper interfaces
- **Accessibility**: Semantic HTML and ARIA attributes
- **Performance**: Optimized with React best practices
- **Mantine Integration**: Leverages Mantine's component system and hooks
- **Next.js**: Compatible with App Router and server components where appropriate
